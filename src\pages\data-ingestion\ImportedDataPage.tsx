import React, { useEffect, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { format, parse } from "date-fns";
import {
  Popover,
  PopoverTrigger,
  PopoverContent,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { useImportData } from "@/hooks/importData/importDataHooks";
import { Loader2 } from "lucide-react";
import GlobalLoader from "@/components/common/GlobalLoader";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

// 1️⃣ Table config with filters
const tableConfig = {
  sales_data: {
    columns: [
      "sale_date",
      "sku_code",
      "location_name",
      "customer_name",
      "quantity",
      "price_per_unit",
      "total_amount",
    ],
    filters: ["Start_date", "End_date", "Location"],
  },
  skus: {
    columns: ["sku_code", "product_name", "description", "price"],
    filters: ["sku_code"],
  },
  products: {
    columns: ["product_name", "product_code", "category_name"],
    filters: ["product_name"],
  },
  locations: {
    columns: [
      "location_name",
      "country",
      "state",
      "city",
      "contact_email",
      "status",
    ],
    filters: ["location_name"],
  },
};

export default function ImportedDataPage() {
  // 3️⃣ UI control states
  const [pendingTable, setPendingTable] = useState("sales_data");
  const [pendingFilters, setPendingFilters] = useState({});
  const [activeTable, setActiveTable] = useState("sales_data");
  const [activeFilters, setActiveFilters] = useState({});
  const [tableData, setTableData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);
  // Import data API call
  const { mutate: fetchImportData } = useImportData();

  console.log("Table Data", tableData)

  const runImportData = (
    table = pendingTable,
    filters = pendingFilters,
    page = currentPage
  ) => {
    const filtersArray = Object.entries(filters)
      .filter(([_, val]) => val !== "" && val !== null)
      .map(([key, value]) => ({
        column: key.toLowerCase(),
        value,
      }));

    setLoading(true);
    fetchImportData(
      {
        table_name: table,
        filters: filtersArray,
        page,
        size: itemsPerPage,
      },
      {
        onSuccess: (res) => {
          if (res.success) {
            setLoading(false);
            setActiveTable(table);
            setActiveFilters(filters);
            setTableData(res.result.data);
            setTotalItems(res.result.total_items);
            setCurrentPage(res.result.current_page);
          }
        },
        onError: (err) => {
          setLoading(false);
          console.error("API Error:", err);
        },
      }
    );
  };

  const handleTableChange = (val: string) => {
    setPendingTable(val);
    setPendingFilters({});
    setCurrentPage(1);
    runImportData(val, {}, 1);
  };

  const handleFilterChange = (key: string, value: string | Date | null) => {
    setPendingFilters((prev) => ({ ...prev, [key]: value }));
  };

  const handleApplyFilter = () => {
    runImportData();
  };

  // Refetch on page change
  useEffect(() => {
    runImportData();
  }, [currentPage]);

  const totalPages = Math.ceil(totalItems / itemsPerPage);

  const formatTableName = (name: string) =>
    name.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());

  // Helper function to determine if a column should be right-aligned (numeric columns)
  const isNumericColumn = (colName: string, data: any[] = []) => {
    if (!Array.isArray(data) || data.length === 0) return false;
    const sampleValue = data[0][colName];
    return typeof sampleValue === "number";
  };

  const isPriceColumn = (colName: string) => {
    const priceKeywords = ["price", "amount", "cost", "total"];
    return priceKeywords.some((keyword) =>
      colName.toLowerCase().includes(keyword)
    );
  };

  const generatePageNumbers = (current: number, total: number) => {
    const delta = 2; // pages to show around current
    const range = [];
    const left = Math.max(2, current - delta);
    const right = Math.min(total - 1, current + delta);

    range.push(1);

    if (left > 2) range.push("...");

    for (let i = left; i <= right; i++) {
      range.push(i);
    }

    if (right < total - 1) range.push("...");

    if (total > 1) range.push(total);

    return range;
  };

  // Helper to format Canadian currency
  const formatCanadianCurrency = (amount: number) => {
    return amount !== null && amount !== undefined
      ? `$${amount.toLocaleString("en-CA", {
        maximumFractionDigits: 2,
        minimumFractionDigits: 2,
      })}`
      : "";
  };

  if (loading) return <GlobalLoader />;

  return (
    <>
      {/* Table Selector */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Imported Data</CardTitle>
        </CardHeader>
        <CardContent>
          <Label htmlFor="table-select">Table Name</Label>
          <Select value={pendingTable} onValueChange={handleTableChange}>
            <SelectTrigger className="w-full max-w-xs">
              <SelectValue placeholder="Select table" />
            </SelectTrigger>
            <SelectContent>
              {Object.keys(tableConfig).map((table) => (
                <SelectItem key={table} value={table}>
                  {formatTableName(table)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </CardContent>
      </Card>

      {/* Filter & Table Section */}
      <Card>
        <CardHeader>
          <CardTitle>{formatTableName(activeTable)}</CardTitle>
        </CardHeader>
        <CardContent>
          {/* Filters */}
          <div className="flex gap-4">
            <div className="flex flex-wrap items-end gap-4 mb-4 w-full">
              {tableConfig[pendingTable]?.filters.map((filterKey) => (
                <div key={filterKey} className="flex-1">
                  <Label>{formatTableName(filterKey)}</Label>
                  {["start_date", "end_date"].includes(
                    filterKey.toLowerCase()
                  ) ? (
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className="w-full justify-start text-left font-normal border-zinc-200"
                        >
                          {pendingFilters[filterKey]
                            ? format(
                              new Date(pendingFilters[filterKey]),
                              "MM-dd-yyyy"
                            )
                            : `Pick ${formatTableName(filterKey)}`}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={
                            pendingFilters[filterKey]
                              ? new Date(pendingFilters[filterKey])
                              : parse("12-30-2025", "MM-dd-yyyy", new Date())
                          }
                          onSelect={(date) =>
                            handleFilterChange(
                              filterKey,
                              date ? format(date, "MM-dd-yyyy") : ""
                            )
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  ) : (
                    <Input
                      placeholder={`Enter ${formatTableName(filterKey)}`}
                      value={pendingFilters[filterKey] || ""}
                      onChange={(e) =>
                        handleFilterChange(filterKey, e.target.value)
                      }
                    />
                  )}
                </div>
              ))}
            </div>
            <div className="flex items-center mt-1">
              <Button onClick={handleApplyFilter}>Apply Filter</Button>
              <Button
                variant="outline"
                className="ml-3"
                onClick={() => {
                  setPendingFilters({});
                  setCurrentPage(1);
                  runImportData(pendingTable, {}, 1);
                }}
              >
                Clear
              </Button>
            </div>
          </div>
          {/* Table */}
          <Table>
            <TableHeader>
              <TableRow>
                {tableConfig[activeTable]?.columns.map((col) => (
                  <TableHead
                    key={col}
                    className={
                      isNumericColumn(col, tableData)
                        ? "text-right"
                        : "text-left"
                    }
                  >
                    {formatTableName(col)}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {
                tableData.length > 0
                  ?
                  <>
                    {tableData?.map((row, idx) => (
                      <TableRow key={idx}>
                        {tableConfig[activeTable]?.columns.map((col) => (
                          <TableCell
                            key={col}
                            className={
                              isNumericColumn(col, tableData)
                                ? "text-right"
                                : "text-left"
                            }
                          >
                            {/* Format date columns */}
                            {
                              col.toLowerCase().includes("date") && row[col]
                                ? format(new Date(row[col]), "MM-dd-yyyy")
                                // format the price or total_amount as currency
                                : ["price_per_unit", "total_amount", "price"].includes(col) && typeof row[col] === "number"
                                  ? formatCanadianCurrency(row[col])
                                  : row[col]
                            }

                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </>
                  :

                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                      No Data Found
                    </TableCell>
                  </TableRow>
              }

            </TableBody>
          </Table>

          {/* Pagination */}
          {
            tableData?.length > 0
            &&
            <div className="flex items-center justify-between mt-4">
              <span className="text-sm text-muted-foreground">
                {totalItems === 0
                  ? "No rows to display"
                  : `Showing ${(currentPage - 1) * itemsPerPage + 1} - ${Math.min(
                    currentPage * itemsPerPage,
                    totalItems
                  )} of ${totalItems} rows`}
              </span>
              <div className="flex items-center gap-2">
                {/* Previous Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                  disabled={currentPage === 1 || totalItems === 0}
                  className="rounded border"
                >
                  <ChevronLeft className="w-4 h-4" />
                </Button>

                {/* Dynamic Page Buttons with Ellipsis */}
                {generatePageNumbers(currentPage, totalPages).map(
                  (page, index) => (
                    <React.Fragment key={index}>
                      {page === "..." ? (
                        <span className="px-2 text-sm text-muted-foreground">
                          ...
                        </span>
                      ) : (
                        <Button
                          variant={currentPage === page ? "default" : "ghost"}
                          size="sm"
                          onClick={() => setCurrentPage(Number(page))}
                          className="rounded border"
                        >
                          {page}
                        </Button>
                      )}
                    </React.Fragment>
                  )
                )}

                {/* Next Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    setCurrentPage((p) => Math.min(totalPages, p + 1))
                  }
                  disabled={currentPage === totalPages || totalItems === 0}
                  className="rounded border"
                >
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          }

        </CardContent>
      </Card>
    </>
  );
}
