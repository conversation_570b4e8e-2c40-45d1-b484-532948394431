import { useState, useEffect, useRef } from "react";
import {
  TrendingUp,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Download,
  Eye,
  EyeOff,
} from "lucide-react";

interface ChartDataPoint {
  date: string;
  value: number;
  confidence_lower?: number;
  confidence_upper?: number;
}

interface InteractiveForecastChartProps {
  historicalData: ChartDataPoint[];
  actualsData: ChartDataPoint[];
  forecastData: ChartDataPoint[];
  isLoading?: boolean;
}

export default function InteractiveForecastChart({
  historicalData,
  actualsData,
  forecastData,
  isLoading = false,
}: InteractiveForecastChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [showHistorical, setShowHistorical] = useState(true);
  const [showActuals, setShowActuals] = useState(true);
  const [showForecast, setShowForecast] = useState(true);
  const [showConfidence, setShowConfidence] = useState(true);
  const [zoom, setZoom] = useState(1);
  const [panX, setPanX] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [lastMouseX, setLastMouseX] = useState(0);
  const [hoveredPoint, setHoveredPoint] = useState<ChartDataPoint | null>(null);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });
  // Get current date in "YYYY-MM-DD" format
  const currentDate = new Date().toISOString().slice(0, 10);

  const allData = [...historicalData, ...actualsData, ...forecastData];
  const maxValue = Math.max(
    ...allData.map((d) => Math.max(d.value, d.confidence_upper || d.value))
  );
  const minValue = Math.min(
    ...allData.map((d) => Math.min(d.value, d.confidence_lower || d.value))
  );
  const padding = (maxValue - minValue) * 0.1;

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width * window.devicePixelRatio;
    canvas.height = rect.height * window.devicePixelRatio;
    ctx.scale(window.devicePixelRatio, window.devicePixelRatio);

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    if (isLoading) {
      // Loading state
      ctx.fillStyle = "#71717a";
      ctx.font = "14px Inter";
      ctx.textAlign = "center";
      ctx.fillText(
        "Loading chart data...",
        canvas.width / (2 * window.devicePixelRatio),
        canvas.height / (2 * window.devicePixelRatio)
      );
      return;
    }

    const chartWidth = rect.width - 80;
    const chartHeight = rect.height - 60;
    const chartLeft = 50;
    const chartTop = 20;

    // Draw grid
    ctx.strokeStyle = "#f1f5f9";
    ctx.lineWidth = 1;

    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = chartTop + (chartHeight / 5) * i;
      ctx.beginPath();
      ctx.moveTo(chartLeft, y);
      ctx.lineTo(chartLeft + chartWidth, y);
      ctx.stroke();
    }

    // Vertical grid lines
    const timePoints = allData.length;
    for (let i = 0; i <= 10; i++) {
      const x = chartLeft + (chartWidth / 10) * i;
      ctx.beginPath();
      ctx.moveTo(x, chartTop);
      ctx.lineTo(x, chartTop + chartHeight);
      ctx.stroke();
    }

    // Helper function to get x position
    const getX = (index: number) => {
      return (
        chartLeft + ((index * chartWidth) / (timePoints - 1)) * zoom + panX
      );
    };

    // Helper function to get y position
    const getY = (value: number) => {
      const normalizedValue =
        (value - minValue + padding) / (maxValue - minValue + 2 * padding);
      return chartTop + chartHeight - normalizedValue * chartHeight;
    };

    // Draw confidence interval for forecast data only
    if (showConfidence && forecastData.length > 0) {
      ctx.fillStyle = "rgba(156, 163, 175, 0.2)"; // Made more transparent
      ctx.beginPath();

      // Start from the last historical point for upper confidence
      if (historicalData.length > 0) {
        const lastHistorical = historicalData[historicalData.length - 1];
        const lastX = getX(historicalData.length - 1);
        const lastY = getY(lastHistorical.value);
        ctx.moveTo(lastX, lastY);
      }

      // Draw upper confidence line for forecast data
      forecastData.forEach((point, index) => {
        if (point.confidence_upper !== undefined) {
          const x = getX(historicalData.length + actualsData.length + index);
          const y = getY(point.confidence_upper);
          ctx.lineTo(x, y);
        }
      });

      // Draw lower confidence line (reverse order) for forecast data
      for (let i = forecastData.length - 1; i >= 0; i--) {
        if (forecastData[i].confidence_lower !== undefined) {
          const x = getX(historicalData.length + actualsData.length + i);
          const y = getY(forecastData[i].confidence_lower);
          ctx.lineTo(x, y);
        }
      }

      // Close back to the starting point
      if (historicalData.length > 0) {
        const lastHistorical = historicalData[historicalData.length - 1];
        const lastX = getX(historicalData.length - 1);
        const lastY = getY(lastHistorical.value);
        ctx.lineTo(lastX, lastY);
      }

      ctx.closePath();
      ctx.fill();
    }

    // Draw historical data line (green solid)
    if (showHistorical) {
      ctx.strokeStyle = "#2b524f"; // Green color
      ctx.lineWidth = 3;
      ctx.setLineDash([]);
      ctx.beginPath();

      // Draw historical data
      historicalData.forEach((point, index) => {
        const x = getX(index);
        const y = getY(point.value);
        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();

      // Draw historical data points
      ctx.fillStyle = "#2b524f";
      historicalData.forEach((point, index) => {
        const x = getX(index);
        const y = getY(point.value);
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, Math.PI * 2);
        ctx.fill();
      });
    }

    // Draw actuals data line (blue solid)
    if (showActuals && actualsData.length > 0) {
      ctx.strokeStyle = "#3b82f6"; // Blue color
      ctx.lineWidth = 3;
      ctx.setLineDash([]);
      ctx.beginPath();

      // Connect to the last historical point if available
      if (historicalData.length > 0) {
        const lastHistorical = historicalData[historicalData.length - 1];
        const lastX = getX(historicalData.length - 1);
        const lastY = getY(lastHistorical.value);
        ctx.moveTo(lastX, lastY);

        // Draw to first actuals point
        const firstActual = actualsData[0];
        const firstX = getX(historicalData.length);
        const firstY = getY(firstActual.value);
        ctx.lineTo(firstX, firstY);
      }

      // Draw actuals data
      actualsData.forEach((point, index) => {
        const x = getX(historicalData.length + index);
        const y = getY(point.value);
        if (index === 0 && historicalData.length === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();

      // Draw actuals data points
      ctx.fillStyle = "#3b82f6";
      actualsData.forEach((point, index) => {
        const x = getX(historicalData.length + index);
        const y = getY(point.value);
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, Math.PI * 2);
        ctx.fill();
      });
    }

    // Draw forecast data line (yellow/lime dashed)
    if (showForecast && forecastData.length > 0) {
      ctx.strokeStyle = "#16a34a"; // Lime color
      ctx.lineWidth = 3;
      ctx.setLineDash([8, 4]);
      ctx.beginPath();

      // Always start from the last historical point
      if (historicalData.length > 0) {
        const lastHistorical = historicalData[historicalData.length - 1];
        const lastX = getX(historicalData.length - 1);
        const lastY = getY(lastHistorical.value);
        ctx.moveTo(lastX, lastY);

        // Draw to first forecast point
        const firstForecast = forecastData[0];
        const firstX = getX(historicalData.length + actualsData.length);
        const firstY = getY(firstForecast.value);
        ctx.lineTo(firstX, firstY);
      } else {
        // If no historical data, start from first forecast point
        const firstX = getX(historicalData.length + actualsData.length);
        const firstY = getY(forecastData[0].value);
        ctx.moveTo(firstX, firstY);
      }

      // Draw forecast data
      forecastData.forEach((point, index) => {
        if (index > 0) {
          // Skip first point as we already drew to it
          const x = getX(historicalData.length + actualsData.length + index);
          const y = getY(point.value);
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();
      ctx.setLineDash([]);

      // Draw forecast data points
      ctx.fillStyle = "#16a34a";
      forecastData.forEach((point, index) => {
        const x = getX(historicalData.length + actualsData.length + index);
        const y = getY(point.value);
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, Math.PI * 2);
        ctx.fill();
      });
    }

    // Draw divider between historical and actuals/forecast (orange dashed)
    if (historicalData.length > 0) {
      const dividerX = getX(historicalData.length - 1);
      ctx.strokeStyle = "black"; // Orange color
      ctx.lineWidth = 2;
      ctx.setLineDash([8, 4]);
      ctx.beginPath();
      ctx.moveTo(dividerX, chartTop);
      ctx.lineTo(dividerX, chartTop + chartHeight);
      ctx.stroke();
      ctx.setLineDash([]);

      // Add "Today" label
      ctx.fillStyle = "#000000";
      ctx.font = "11px Inter";
      ctx.textAlign = "center";
      ctx.fillText("Historical Data Cutoff", dividerX, chartTop - 8);
    }

    // Draw blue dashed line for current date marker
    if (currentDate) {
      // Find the index of the current date in the combined data
      const allCombinedData = [
        ...historicalData,
        ...actualsData,
        ...forecastData,
      ];
      const currentDateIndex = allCombinedData.findIndex(
        (point) => point.date === currentDate
      );

      if (currentDateIndex !== -1) {
        const currentDateX = getX(currentDateIndex);
        ctx.strokeStyle = "#AB732B"; // Blue color
        ctx.lineWidth = 2;
        ctx.setLineDash([8, 4]);
        ctx.beginPath();
        ctx.moveTo(currentDateX, chartTop);
        ctx.lineTo(currentDateX, chartTop + chartHeight);
        ctx.stroke();
        ctx.setLineDash([]);

        // Add "Today" label
        ctx.fillStyle = "#AB732B";
        ctx.font = "11px Inter";
        ctx.textAlign = "center";
        ctx.fillText("Today", currentDateX, chartTop - 5);
      }
    } else if (actualsData.length > 0) {
      // Fallback: Position at the end of actuals data if no currentDate provided
      const currentDateX = getX(historicalData.length + actualsData.length - 1);
      ctx.strokeStyle = "#2563eb"; // Blue color
      ctx.lineWidth = 2;
      ctx.setLineDash([6, 4]);
      ctx.beginPath();
      ctx.moveTo(currentDateX, chartTop);
      ctx.lineTo(currentDateX, chartTop + chartHeight);
      ctx.stroke();
      ctx.setLineDash([]);

      // Add "Today" label
      ctx.fillStyle = "#2563eb";
      ctx.font = "11px Inter";
      ctx.textAlign = "center";
      ctx.fillText("Today", currentDateX, chartTop - 5);
    }

    // Draw y-axis labels
    ctx.fillStyle = "#71717a";
    ctx.font = "12px Inter";
    ctx.textAlign = "right";
    for (let i = 0; i <= 5; i++) {
      const value = minValue + ((maxValue - minValue) / 5) * (5 - i);
      const y = chartTop + (chartHeight / 5) * i;
      ctx.fillText(
        `${Math.round(value).toLocaleString()}`,
        chartLeft - 10,
        y + 4
      );
    }

    // Draw x-axis labels
    ctx.textAlign = "center";
    const labelInterval = Math.max(1, Math.floor(timePoints / 8));
    allData.forEach((point, index) => {
      if (index % labelInterval === 0) {
        const x = getX(index);
        const date = new Date(point.date);
        const label = date.toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
        });
        ctx.fillText(label, x, chartTop + chartHeight + 20);
      }
    });

    // Draw legend labels
    // ctx.font = '11px Inter';
    // ctx.fillStyle = '#374151';
    // ctx.fillText('Historical Data', chartLeft + 60, chartTop + chartHeight + 45);
    // ctx.fillText('Current Actuals', chartLeft + 180, chartTop + chartHeight + 45);
    // ctx.fillText('(up to today)', chartLeft + 180, chartTop + chartHeight + 60);
    // ctx.fillText('Future Forecast', chartLeft + 320, chartTop + chartHeight + 45);
    // ctx.fillText('(from today onwards)', chartLeft + 320, chartTop + chartHeight + 60);
  }, [
    allData,
    showHistorical,
    showActuals,
    showForecast,
    showConfidence,
    zoom,
    panX,
    isLoading,
    historicalData,
    actualsData,
    forecastData,
    currentDate,
  ]);

  // Completely disable mouse interactions except hover
  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;

    setMousePos({ x: e.clientX, y: e.clientY });

    // Only handle hover detection - NO DRAGGING
    const chartLeft = 50;
    const chartWidth = rect.width - 80;
    const timePoints = allData.length;

    const closestIndex = Math.round(((x - chartLeft) * (timePoints - 1)) / chartWidth);

    if (closestIndex >= 0 && closestIndex < allData.length) {
      setHoveredPoint(allData[closestIndex]);
    } else {
      setHoveredPoint(null);
    }
  };

  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev * 1.5, 5));
  };

  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev / 1.5, 0.5));
  };

  const handleReset = () => {
    setZoom(1);
    // Remove setPanX(0);
  };

  const handleExport = () => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const link = document.createElement("a");
    link.download = "forecast-chart.png";
    link.href = canvas.toDataURL();
    link.click();
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-CA", {
      style: "currency",
      currency: "CAD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  return (
    <div style={{ position: "relative", width: "100%" }}>
      {/* Chart Controls */}
      <div
        style={{
          display: "flex",
          justifyContent: "flex-end",
          gap: "0.5rem",
          zIndex: 10,
        }}
      >
        <button
          onClick={() => setShowHistorical(!showHistorical)}
          style={{
            display: "flex",
            alignItems: "center",
            gap: "0.25rem",
            padding: "0.375rem 0.75rem",
            fontSize: "0.75rem",
            fontWeight: "500",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: showHistorical ? "#2b524f" : "white",
            color: showHistorical ? "white" : "#374151",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
        >
          {showHistorical ? <Eye size={12} /> : <EyeOff size={12} />}
          Historical
        </button>

        <button
          onClick={() => setShowActuals(!showActuals)}
          style={{
            display: "flex",
            alignItems: "center",
            gap: "0.25rem",
            padding: "0.375rem 0.75rem",
            fontSize: "0.75rem",
            fontWeight: "500",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: showActuals ? "#3b82f6" : "white",
            color: showActuals ? "white" : "#374151",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
        >
          {showActuals ? <Eye size={12} /> : <EyeOff size={12} />}
          Actuals
        </button>

        <button
          onClick={() => setShowForecast(!showForecast)}
          style={{
            display: "flex",
            alignItems: "center",
            gap: "0.25rem",
            padding: "0.375rem 0.75rem",
            fontSize: "0.75rem",
            fontWeight: "500",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: showForecast ? "#16a34a" : "white",
            color: showForecast ? "white" : "#374151",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
        >
          {showForecast ? <Eye size={12} /> : <EyeOff size={12} />}
          Forecast
        </button>

        <button
          onClick={() => setShowConfidence(!showConfidence)}
          style={{
            display: "flex",
            alignItems: "center",
            gap: "0.25rem",
            padding: "0.375rem 0.75rem",
            fontSize: "0.75rem",
            fontWeight: "500",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: showConfidence ? "#6e747e4d" : "white",
            color: showConfidence ? "#374151" : "#374151",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
        >
          {showConfidence ? <Eye size={12} /> : <EyeOff size={12} />}
          Confidence
        </button>
      </div>

      {/* Main Chart Canvas */}
      <div className="overflow-hidden pt-2 h-[400px]">
        <canvas
          ref={canvasRef}
          style={{
            width: "100%",
            height: "100%",
            cursor: "pointer",
            userSelect: "none",
            WebkitUserSelect: "none",
            pointerEvents: "auto",
          }}
          onMouseMove={handleMouseMove}
          onMouseLeave={() => {
            setHoveredPoint(null);
          }}
          onMouseDown={(e) => e.preventDefault()}
          onDragStart={(e) => e.preventDefault()}
          draggable={false}
        />
      </div>
      {/* Tooltip */}
      {hoveredPoint && (
        <div
          style={{
            position: "fixed",
            left: mousePos.x + 10,
            top: mousePos.y - 10,
            backgroundColor: "rgba(0, 0, 0, 0.9)",
            color: "white",
            padding: "0.75rem",
            borderRadius: "0.375rem",
            fontSize: "0.75rem",
            zIndex: 1000,
            pointerEvents: "none",
            transform: "translate(0, -100%)",
          }}
        >
          <div style={{ fontWeight: "600", marginBottom: "0.25rem" }}>
            {new Date(hoveredPoint.date).toLocaleDateString()}
          </div>
          <div style={{ color: "#34d399" }}>
            Value: {(hoveredPoint.value)}
          </div>
          {hoveredPoint.confidence_lower !== undefined &&
            hoveredPoint.confidence_upper !== undefined && (
              <div style={{ color: "#d1d5db", fontSize: "0.625rem" }}>
                Range: {(hoveredPoint.confidence_lower)} -{" "}
                {(hoveredPoint.confidence_upper)}
              </div>
            )}
        </div>
      )}

      {/* Chart Legend */}
      <div
        style={{
          display: "flex",
          gap: "1rem",
          fontSize: "0.75rem",
          justifyContent: "center",
          marginTop: "1rem",
          color: "#71717a",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "0.25rem" }}>
          <div
            style={{ width: "12px", height: "4px", backgroundColor: "#2b524f" }}
          ></div>
          Historical Data
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "0.25rem" }}>
          <div
            style={{ width: "12px", height: "4px", backgroundColor: "#3b82f6" }}
          ></div>
          Actuals Data
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "0.25rem" }}>
          <div
            style={{
              width: "12px",
              height: "4px",
              backgroundColor: "#16a34a",
              borderTop: "2px dashed #16a34a",
              borderBottom: "1px solid transparent",
            }}
          ></div>
          Forecast Data
        </div>
        <div style={{ display: "flex", alignItems: "center", gap: "0.25rem" }}>
          <div
            style={{
              width: "12px",
              height: "8px",
              backgroundColor: "rgba(110, 116, 126, 0.3)",
            }}
          ></div>
          Confidence Interval
        </div>
      </div>

      {/* Zoom Controls */}
      <div
        style={{
          display: "flex",
          justifyContent: "flex-end",
          gap: "0.25rem",
          zIndex: 10,
        }}
      >
        <button
          disabled
          onClick={handleZoomIn}
          style={{
            padding: "0.5rem",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: "white",
            color: "#374151",
            cursor: "not-allowed",
            transition: "all 0.2s",
          }}
          title="Zoom In"
        >
          <ZoomIn size={14} />
        </button>

        <button
          disabled
          onClick={handleZoomOut}
          style={{
            padding: "0.5rem",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: "white",
            color: "#374151",
            cursor: "not-allowed",
            transition: "all 0.2s",
          }}
          title="Zoom Out"
        >
          <ZoomOut size={14} />
        </button>

        <button
          onClick={handleReset}
          style={{
            padding: "0.5rem",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: "white",
            color: "#374151",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
          title="Reset View"
        >
          <RotateCcw size={14} />
        </button>

        <button
          onClick={handleExport}
          style={{
            padding: "0.5rem",
            borderRadius: "0.375rem",
            border: "1px solid #e4e4e7",
            backgroundColor: "white",
            color: "#374151",
            cursor: "pointer",
            transition: "all 0.2s",
          }}
          title="Export Chart"
        >
          <Download size={14} />
        </button>
      </div>
    </div>
  );
}
