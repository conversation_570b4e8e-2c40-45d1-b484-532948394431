import { useState, useEffect, useMemo } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  Download,
  Upload,
  FileText,
  ArrowRight,
  AlertCircle,
  Minus,
  Eye,
  Sheet,
  Info,
  CircleQuestionMark,
  CheckCheck,
  Check,
} from "lucide-react";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";
import ProcessStepper from "@/components/data-ingestion/ProcessStepper";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { usePagination } from "@/hooks/usePagination";
import { Pagination } from "@/components/common/Pagination";
import {
  salesMockData,
  locationsMockData,
  skusMockData,
  getTableData,
} from "@/data/validationMockData";
import { Card, CardHeader, CardContent, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import checkTick from "../../assets/Tick.png";
import crossIcon from "../../assets/Error.png";
import UploadValidateFileModal from "@/components/data-ingestion/UploadValidateFileModal";
import { useGetValidationResults } from "@/hooks/fileValidate/fileValidateHooks";
import {
  TableValidationStatus,
  useGetTableValidationStatus,
} from "@/hooks/fileValidate/tableValidationStatusHooks";
import { useGetMetaDownload } from "@/hooks/batchFilesUploads/fileDownload";
import { formatDateValue } from "@/constants/formatDate";
import { useImportCorrectedData } from "@/hooks/fileValidate/useImportCorrectedDataHooks";
import GlobalLoader from "@/components/common/GlobalLoader";

interface TableValidation {
  id: string;
  name: string;
  description: string;
  totalRows: number;
  validRows: number;
  invalidRows: number;
  missingRows: number;
  status: "not-validated" | "partially-validated" | "validated" | "failed";
  issues: ValidationIssue[];
  correctedFileUploaded: boolean;
}

interface ValidationIssue {
  id: string;
  field: string;
  rowNumber: number;
  issueType: "missing" | "invalid" | "format" | "range";
  message: string;
  currentValue: string;
  expectedFormat?: string;
  severity: "error" | "warning";
}

interface ValidationPageProps {
  handleStepper: (show: boolean, step: number) => void;
  selectedBatchId?: string | null;
}

export default function ValidationPage({
  handleStepper,
  selectedBatchId,
}: ValidationPageProps) {
  const { batchId } = useParams();
  const navigate = useNavigate();
  const { toasts, removeToast, success, error } = useToast();

  const [salesData, setSalesData] = useState(salesMockData);
  const [locationsData, setLocationsData] = useState(locationsMockData);
  const [skusData, setSkusData] = useState(skusMockData);
  const [isLoadingData, setIsLoadingData] = useState(false);
  const [showUploadValidateModal, setShowUploadValidateModal] = useState(false);
  const [downloadEnabled, setDownloadEnabled] = useState(false);
  const [areAllTablesValidated, setAreAllTablesValidated] = useState(false);
  const [page, setPage] = useState(1);
  const [size] = useState(10);
  const [isLoadingImport, setIsLoadingImport] = useState(false);

  // const salesPagination = usePagination({ data: salesData, itemsPerPage: 10 });
  const locationsPagination = usePagination({
    data: locationsData,
    itemsPerPage: 10,
  });
  const skusPagination = usePagination({ data: skusData, itemsPerPage: 10 });

  // State for tracking validation status of each table
  const [tableValidationStatus, setTableValidationStatus] = useState({
    sales_data: "not-validated",
    locations: "not-validated",
    skus: "not-validated",
  });

  // State for tracking if tables have been downloaded
  const [tableDownloaded, setTableDownloaded] = useState({
    sales_data: false,
    locations: false,
    skus: false,
  });

  // State for tracking if corrected files have been uploaded
  const [tableCorrectedFileUploaded, setTableCorrectedFileUploaded] = useState({
    sales_data: false,
    locations: false,
    skus: false,
  });

  const [tableValidations, setTableValidations] = useState<TableValidation[]>([
    {
      id: "sales",
      name: "Sales Transactions",
      description: "Primary sales transaction data validation",
      totalRows: 1247,
      validRows: 1098,
      invalidRows: 89,
      missingRows: 60,
      status: "not-validated",
      correctedFileUploaded: false,
      issues: [
        {
          id: "issue1",
          field: "sku",
          rowNumber: 45,
          issueType: "missing",
          message: "SKU field is empty",
          currentValue: "",
          severity: "error",
        },
        {
          id: "issue2",
          field: "unit_price",
          rowNumber: 234,
          issueType: "invalid",
          message: "Unit price cannot be negative",
          currentValue: "-15.99",
          expectedFormat: "Positive decimal number",
          severity: "error",
        },
        {
          id: "issue3",
          field: "transaction_date",
          rowNumber: 127,
          issueType: "format",
          message: "Invalid date format",
          currentValue: "2024/01/15",
          expectedFormat: "YYYY-MM-DD HH:MM:SS",
          severity: "error",
        },
        {
          id: "issue4",
          field: "customer_id",
          rowNumber: 89,
          issueType: "format",
          message: "Customer ID format is non-standard",
          currentValue: "customer_123",
          expectedFormat: "CUST-XXXXX",
          severity: "warning",
        },
      ],
    },
    {
      id: "products",
      name: "Product Catalog",
      description: "Product information validation",
      totalRows: 456,
      validRows: 425,
      invalidRows: 31,
      missingRows: 0,
      status: "not-validated",
      correctedFileUploaded: false,
      issues: [
        {
          id: "issue5",
          field: "category",
          rowNumber: 12,
          issueType: "missing",
          message: "Product category is required",
          currentValue: "",
          severity: "error",
        },
        {
          id: "issue6",
          field: "retail_price",
          rowNumber: 67,
          issueType: "range",
          message: "Retail price must be greater than cost price",
          currentValue: "15.99",
          expectedFormat: "Must be > cost_price (19.99)",
          severity: "error",
        },
      ],
    },
    {
      id: "customers",
      name: "Customer Information",
      description: "Customer data validation",
      totalRows: 892,
      validRows: 892,
      invalidRows: 0,
      missingRows: 0,
      status: "validated",
      correctedFileUploaded: false,
      issues: [],
    },
  ]);

  const [expandedTables, setExpandedTables] = useState<string[]>(["sales"]);
  const [uploadingFiles, setUploadingFiles] = useState<Record<string, boolean>>(
    {}
  );

  useEffect(() => {
    const loadTableData = async () => {
      setIsLoadingData(true);
      try {
        const flashanaSchemaId = "schema_123";
        const uploadedFileBatchId = batchId || "batch_456";

        const data = await getTableData(flashanaSchemaId, uploadedFileBatchId);

        setSalesData(data.sales_data);
        setLocationsData(data.locations);
        setSkusData(data.skus);
      } catch (error) {
        console.error("Error loading table data:", error);
      } finally {
        setIsLoadingData(false);
      }
    };

    loadTableData();
  }, [batchId]);

  const getOverallStatus = () => {
    const allValidated = Object.values(tableValidationStatus).every(
      (status) => status === "validated"
    );
    const someValidated = Object.values(tableValidationStatus).some(
      (status) => status === "validated"
    );

    if (allValidated) return "Validated";
    if (someValidated) return "Partially Validated";
    return "Not Validated";
  };

  const canImport = () => {
    return Object.values(tableValidationStatus).every(
      (status) => status === "validated"
    );
  };

  const toggleTableExpansion = (tableId: string) => {
    setExpandedTables((prev) =>
      prev.includes(tableId)
        ? prev.filter((id) => id !== tableId)
        : [...prev, tableId]
    );
  };

  const handleDownloadFile = (tableId: string) => {
    // Simulate file download
    const tableName =
      tableId === "sales_data"
        ? "Sales_data"
        : tableId === "locations"
          ? "Locations"
          : "SKUs";
    const blob = new Blob(
      [`Issues for ${tableName}\n\nField,Row,Issue,Current Value,Expected\n`],
      { type: "text/csv" }
    );

    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${tableId}_validation_issues.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    // Mark table as downloaded
    setTableDownloaded((prev) => ({ ...prev, [tableId]: true }));

    // Check if both download and upload have been done for this table
    if (tableCorrectedFileUploaded[tableId]) {
      setTableValidationStatus((prev) => ({ ...prev, [tableId]: "validated" }));
    }

    success(
      "File Downloaded",
      `Validation issues for ${tableName} downloaded successfully.`
    );
  };

  const handleUploadCorrectedFile = (tableId: string) => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".csv,.xlsx,.xls";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        setUploadingFiles((prev) => ({ ...prev, [tableId]: true }));

        // Simulate file upload and validation
        setTimeout(() => {
          const tableName =
            tableId === "sales_data"
              ? "Sales_data"
              : tableId === "locations"
                ? "Locations"
                : "SKUs";

          // Mark table as having corrected file uploaded
          setTableCorrectedFileUploaded((prev) => ({
            ...prev,
            [tableId]: true,
          }));

          // Check if both download and upload have been done for this table
          if (tableDownloaded[tableId]) {
            setTableValidationStatus((prev) => ({
              ...prev,
              [tableId]: "validated",
            }));
          }

          setUploadingFiles((prev) => ({ ...prev, [tableId]: false }));
          success(
            "File Uploaded",
            `Corrected file for ${tableName} uploaded and validated successfully.`
          );
        }, 2000);
      }
    };
    input.click();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "validated":
        return <CheckCircle size={16} style={{ color: "#16a34a" }} />;
      case "partially-validated":
        return <AlertCircle size={16} style={{ color: "#d97706" }} />;
      case "failed":
        return <XCircle size={16} style={{ color: "#dc2626" }} />;
      default:
        return <Minus size={16} style={{ color: "#71717a" }} />;
    }
  };

  const overallStatus = getOverallStatus();

  // FOR DOWNLOADING FILE BUTTON CLICK
  const downloadFile = (batchId: Number) => {
    console.log("donload calll");
    setDownloadEnabled(true);
  };

  // API call for downloading file from S3 bucket
  const { data: downloadData, isLoading: downloadLoading } = useGetMetaDownload(
    Number(selectedBatchId),
    downloadEnabled
  );

  useEffect(() => {
    refetch();
  }, []);

  // Handle download file API response
  useEffect(() => {
    if (downloadEnabled && downloadData?.result?.data?.download_url) {
      window.open(downloadData.result.data.download_url, "_blank");
      setDownloadEnabled(false);
    }
  }, [downloadData, downloadEnabled]);

  // Request payload for validation result API
  const validationParams = useMemo(
    () => ({
      batch_id: Number(selectedBatchId),
      page,
      size,
    }),
    [selectedBatchId, page, size]
  );

  // API call to get validation result data
  const {
    data,
    isLoading: isValidationResultLoading,
    error: errorValidationResult,
    isFetching: isValidationResultFetching,
    refetch,
  } = useGetValidationResults(validationParams);

  // Destructure values safely from validation result API response
  const invalidRecords = data?.result.data.invalid_records ?? [];
  const batchInfo = data?.result.data.batch_info;
  const totalCount = data?.result?.total_items ?? 0;
  const totalPages = Math.ceil(totalCount / size);

  // Handle pagination for table from API response
  const handlePageChange = (newPage: number) => {
    setPage(newPage); // this updates the memoized params
  };

  // API call to get status of tables (validated or not validated)
  const {
    data: tableStatusData,
    isLoading: isTableStatusLoading,
    isError: isTableStatusError,
    isFetching: isTableStatusFetching,
    refetch: statusRefetch,
  } = useGetTableValidationStatus(Number(selectedBatchId));

  // Destructure values safely from table status API response
  const tableStatus = tableStatusData?.result?.data as
    | TableValidationStatus
    | undefined;

  // Handle tables status from API response and update it on the UI
  const tableEntries = tableStatus
    ? Object.entries(tableStatus).filter(
        ([key]) => key !== "consolidated_status"
      )
    : [];

  const formatLabel = (key: string) =>
    key
      .replace(/^is_/, "")
      .replace(/_valid$/, "")
      .replace(/_/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());

  // Handle consolidated status for all tables from API response and update it on the UI
  const consolidatedStatus = tableStatus?.consolidated_status;
  let overallTablesStatus:
    | "Validated"
    | "Partially Validated"
    | "Not Validated" = "Not Validated";

  if (consolidatedStatus === "valid") {
    overallTablesStatus = "Validated";
  } else if (consolidatedStatus === "partial") {
    overallTablesStatus = "Partially Validated";
  }

  useEffect(() => {
    if (consolidatedStatus === "valid") {
      setAreAllTablesValidated(true);
    } else {
      setAreAllTablesValidated(false);
    }
  }, [consolidatedStatus]);

  // Handle upload corrected file callback from validate popup
  const handleValidateComplete = async () => {
    setShowUploadValidateModal(false);
    refetch();
    statusRefetch();
  };

  // Import corrected data API call
  const { mutate: importCorrectedData, isSuccess } = useImportCorrectedData();

  const handleImport = () => {
    setIsLoadingImport(true);
    importCorrectedData(Number(selectedBatchId), {
      onSuccess: (response) => {
        setIsLoadingImport(false);
        success("File Imported", response?.message);
        setTimeout(async () => {
          navigate("/data-ingestion/imported");
        }, 1500);
      },
      onError: (error) => {
        setIsLoadingImport(false);
        console.error("Import failed:", error?.message);
      },
    });
  };

  const isLoading = isValidationResultLoading || isTableStatusLoading;
  const isRefetching = isValidationResultFetching || isTableStatusFetching;

  if (isLoading || isRefetching) return <GlobalLoader />;
  if (errorValidationResult)
    return <p>Error loading batch list: {errorValidationResult.message}</p>;

  return (
    <div>
      {/* Process Stepper */}

      {/* Main Layout */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Validation Tables Section */}
        <div className="md:col-span-2">
          {/* Header */}
          <Card className="mb-4">
            <CardHeader className="p-4 border-0">
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  className="border-none w-6 h-6"
                  size="icon"
                  onClick={() => handleStepper(false, 1)}
                >
                  <ArrowLeft size={16} />
                </Button>
                <div className="flex gap-12">
                  <div>
                    <div className="text-sm text-zinc-600">Batch Name</div>
                    <span className="text-zinc-900 font-semibold">
                      {batchInfo?.batch_name ?? "--"}
                    </span>
                  </div>
                  <div>
                    <div className="text-sm text-zinc-600">Uploaded Date</div>
                    <span className="text-zinc-900 font-semibold">
                      {batchInfo?.uploaded_at
                        ? new Date(batchInfo.uploaded_at)
                            .toLocaleDateString("en-US", {
                              month: "2-digit",
                              day: "2-digit",
                              year: "numeric",
                            })
                            .replace(/\//g, "-")
                        : "--"}
                    </span>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>
          <Card className="mb-6">
            {/* <CardHeader>
              <CardTitle className="text-xs font-[400] text-zinc-600">
                Table Name
                <div className="text-base font-semibold text-zinc-800">
                  <span>Sales_data</span>
                </div>
              </CardTitle>
            </CardHeader> */}
            <CardContent className="p-0">
              {/* Accordions for tables */}
              {/* Sales_data Accordion */}
              <Accordion type="single" collapsible defaultValue="sales">
                <AccordionItem value="sales">
                  <AccordionTrigger>
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        {/* <img src={checkTick} /> */}
                        <div className="gap-1">
                          <span className="text-zinc-900 font-semibold">
                            File Type
                          </span>
                        </div>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    {/* Info Label */}
                    <div className="p-4 pb-0 rounded text-cyan-700 text-sm font-medium flex items-center justify-between">
                      <span className="flex gap-2">
                        <Info size={20} className="min-w-[1rem]" />{" "}
                        <span>
                          Rows with validation errors are shown here. Valid rows
                          have been stored successfully.
                        </span>
                      </span>
                    </div>
                    <div className="flex gap-2 p-4 pb-0">
                      <Button
                        onClick={(e) => {
                          e.stopPropagation();
                          downloadFile(Number(selectedBatchId));
                        }}
                        className="flex items-center gap-2"
                        variant="outline"
                        disabled={areAllTablesValidated}
                      >
                        <Download className="h-4 w-4" />
                        Download File
                      </Button>
                      <Button
                        onClick={() => setShowUploadValidateModal(true)}
                        className="flex items-center gap-2"
                        variant="default"
                        disabled={areAllTablesValidated}
                      >
                        <Upload className="h-4 w-4" />
                        Upload Corrected File
                      </Button>
                    </div>
                    {areAllTablesValidated && (
                      <div className="p-16 text-center text-gray-500 text-md">
                        Validation complete! All records in this batch are now
                        verified.
                      </div>
                    )}
                    {/* <div className="p-16 text-center text-gray-500 text-md">
                      Validation complete! All records in this batch are now
                      verified.
                    </div> */}
                    <div className="overflow-x-auto p-4">
                      <Table className="w-full">
                        <TableHeader>
                          <TableRow className="bg-gray-50">
                            {invalidRecords.length > 0 &&
                              Object.keys(invalidRecords[0])
                                .filter(
                                  (key) =>
                                    key !== "missing_data_keys" &&
                                    key !== "invalid_data_keys"
                                )
                                .map((key) => (
                                  <TableHead
                                    key={key}
                                    className="text-left py-2 px-4 font-medium whitespace-nowrap"
                                  >
                                    {key}
                                  </TableHead>
                                ))}
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {invalidRecords.map((row, rowIndex) => (
                            <TableRow key={rowIndex} className="border-b">
                              {Object.entries(row)
                                .filter(
                                  ([key]) =>
                                    key !== "missing_data_keys" &&
                                    key !== "invalid_data_keys"
                                )
                                .map(([key, value]) => {
                                  const isMissing =
                                    row.missing_data_keys?.includes(key);
                                  const isInvalid =
                                    row.invalid_data_keys?.includes(key);

                                  return (
                                    <TableCell key={key} className="py-2 px-4">
                                      {isMissing ? (
                                        <span className="text-rose-700 flex items-center gap-1">
                                          <CircleQuestionMark className="h-4 w-4" />
                                          Missing
                                        </span>
                                      ) : isInvalid ? (
                                        <span className="flex items-center gap-1 text-rose-700">
                                          <span className="text-gray-900">
                                            {formatDateValue(value)}
                                          </span>
                                          Invalid
                                        </span>
                                      ) : (
                                        formatDateValue(value)
                                      )}
                                    </TableCell>
                                  );
                                })}
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                      <div className="flex items-center justify-between mt-6 text-sm text-gray-500">
                        {totalPages > 1 && (
                          <>
                            <span>
                              Showing {(page - 1) * size + 1}–
                              {Math.min(page * size, totalCount)} of{" "}
                              {totalCount} rows
                            </span>
                            <div className="flex gap-2">
                              {/* Previous Button */}
                              <Button
                                variant="ghost"
                                className="border rounded"
                                size="sm"
                                disabled={page === 1}
                                onClick={() => setPage(page - 1)}
                              >
                                {"<"}
                              </Button>

                              {/* Page Numbers */}
                              {[...Array(totalPages)].map((_, i) => {
                                const pageNum = i + 1;
                                return (
                                  <Button
                                    key={pageNum}
                                    size="sm"
                                    className="rounded border"
                                    variant={
                                      page === pageNum ? "default" : "ghost"
                                    }
                                    onClick={() => setPage(pageNum)}
                                  >
                                    {pageNum}
                                  </Button>
                                );
                              })}

                              {/* Next Button */}
                              <Button
                                variant="ghost"
                                size="sm"
                                className="rounded border"
                                disabled={page === totalPages}
                                onClick={() => setPage(page + 1)}
                              >
                                {">"}
                              </Button>
                            </div>
                          </>
                        )}
                      </div>
                    </div>
                    {/* <div className="flex flex-col gap-3 text-sm border-t p-4">
                      <div className="flex items-center gap-2 text-green-600">
                        <img src={checkTick} />
                        {salesData.filter((row) => !row.hasError).length} rows
                        valid
                      </div>
                      <div className="flex items-center gap-2 text-red-600">
                        <img src={crossIcon} />
                        {salesData.filter((row) => row.hasError).length} rows
                        with issues -
                        {
                          salesData.filter((row) => row.errorType === "missing")
                            .length
                        }{" "}
                        missing values,
                        {
                          salesData.filter((row) => row.errorType === "invalid")
                            .length
                        }{" "}
                        invalid data types
                      </div>
                    </div> */}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card>

          {/* Locations Accordion */}
          {/* <Card className="mb-6">
            <CardContent className="p-0">
              <Accordion type="single" collapsible>
                <AccordionItem value="locations">
                  <AccordionTrigger>
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <img src={checkTick} />
                        <div className="gap-1 flex flex-col justify-center">
                          <div className="text-sm text-zinc-600 font-[400]">
                            Table Name
                          </div>
                          <span className="text-zinc-900 font-semibold text-left">
                            Locations
                          </span>
                        </div>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="flex gap-2 p-4 pb-0">
                      <Button
                        onClick={() => handleDownloadFile("locations")}
                        className="flex items-center gap-2"
                        variant="outline"
                      >
                        <Download className="h-4 w-4" />
                        Download File
                      </Button>
                      <Button
                        onClick={() => setShowUploadValidateModal(true)}
                        className="flex items-center gap-2"
                        variant="default"
                      >
                        <Upload className="h-4 w-4" />
                        Upload Corrected File
                      </Button>
                    </div>
                    <div className="overflow-x-auto p-4 pb-0">
                      <Table className="w-full">
                        <TableHeader>
                          <TableRow className="bg-gray-50 border-b">
                            <TableHead className="text-left py-2 px-4 font-medium">
                              location_id
                            </TableHead>
                            <TableHead className="text-left py-2 px-4 font-medium">
                              location_name
                            </TableHead>
                            <TableHead className="text-left py-2 px-4 font-medium">
                              region
                            </TableHead>
                            <TableHead className="text-left py-2 px-4 font-medium">
                              country
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {locationsPagination.paginatedData.map(
                            (row, index) => (
                              <TableRow key={index} className="border-b">
                                <TableCell className="py-2 px-4">{row.location_id}</TableCell>
                                <TableCell className="py-2 px-4">{row.location_name}</TableCell>
                                <TableCell className="py-2 px-4">
                                  {row.hasError && row.errorType === "missing" ? (
                                    <span className="text-red-600 flex items-center gap-1">
                                      <AlertTriangle className="h-4 w-4" />
                                      Missing
                                    </span>
                                  ) : (
                                    row.region
                                  )}
                                </TableCell>
                                <TableCell className="py-2 px-4">
                                  {row.hasError && row.errorType === "missing" ? (
                                    <span className="text-red-600 flex items-center gap-1">
                                      <AlertTriangle className="h-4 w-4" />
                                      Missing
                                    </span>
                                  ) : (
                                    row.country
                                  )}
                                </TableCell>
                              </TableRow>
                            )
                          )}
                        </TableBody>
                      </Table>
                      <Pagination
                        currentPage={locationsPagination.currentPage}
                        totalPages={locationsPagination.totalPages}
                        onPageChange={locationsPagination.goToPage}
                        totalItems={locationsPagination.totalItems}
                        itemsPerPage={10}
                      />
                    </div>
                    <div className="flex flex-col gap-3 text-sm border-t p-4">
                      <div className="flex items-center gap-2 text-green-600">
                        <img src={checkTick} />
                        {
                          locationsData.filter((row) => !row.hasError).length
                        }{" "}
                        rows valid
                      </div>
                      {locationsData.filter((row) => row.hasError).length >
                        0 && (
                        <div className="flex items-center gap-2 text-red-600">
                          <img src={crossIcon} />
                          {
                            locationsData.filter((row) => row.hasError).length
                          }{" "}
                          rows with issues -
                          {
                            locationsData.filter(
                              (row) => row.errorType === "missing"
                            ).length
                          }{" "}
                          missing values
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card> */}

          {/* SKUs Accordion */}
          {/* <Card className="mb-6">
            <CardContent className="p-0">
              <Accordion type="single" collapsible>
                <AccordionItem value="skus">
                  <AccordionTrigger>
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <img src={crossIcon} />
                        <div className="flex flex-col gap-1">
                          <div className="text-sm text-zinc-600 font-[400]">
                            Table Name
                          </div>
                          <span className="text-zinc-900 font-semibold text-left">
                            SKUs
                          </span>
                        </div>
                      </div>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent>
                    <div className="flex gap-2 p-4 pb-0">
                      <Button
                        onClick={() => handleDownloadFile("skus")}
                        className="flex items-center gap-2"
                        variant="outline"
                      >
                        <Download className="h-4 w-4" />
                        Download File
                      </Button>
                      <Button
                        onClick={() => setShowUploadValidateModal(true)}
                        className="flex items-center gap-2"
                        variant="default"
                      >
                        <Upload className="h-4 w-4" />
                        Upload Corrected File
                      </Button>
                    </div>
                    <div className="overflow-x-auto p-4 pb-0">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="border-b bg-gray-50">
                            <th className="text-left py-2 px-4 font-medium">
                              sku_id
                            </th>
                            <th className="text-left py-2 px-4 font-medium">
                              product_name
                            </th>
                            <th className="text-left py-2 px-4 font-medium">
                              category
                            </th>
                            <th className="text-left py-2 px-4 font-medium">
                              price
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {skusPagination.paginatedData.map((row, index) => (
                            <tr
                              key={index}
                              className={cn(
                                "border-b",
                                row.errorType === "duplicate" && "bg-red-50"
                              )}
                            >
                              <td className="py-2 px-4">
                                {row.sku_id}
                                {row.errorType === "duplicate" && (
                                  <span className="text-red-600 text-xs ml-2">
                                    (duplicate)
                                  </span>
                                )}
                              </td>
                              <td className="py-2 px-4">{row.product_name}</td>
                              <td className="py-2 px-4">
                                {row.hasError && row.errorType === "missing" ? (
                                  <span className="text-red-600 flex items-center gap-1">
                                    <AlertTriangle className="h-4 w-4" />
                                    Missing
                                  </span>
                                ) : (
                                  row.category
                                )}
                              </td>
                              <td className="py-2 px-4">
                                {row.hasError && row.errorType === "missing" ? (
                                  <span className="text-red-600 flex items-center gap-1">
                                    <AlertTriangle className="h-4 w-4" />
                                    Missing
                                  </span>
                                ) : (
                                  row.price
                                )}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                      <Pagination
                        currentPage={skusPagination.currentPage}
                        totalPages={skusPagination.totalPages}
                        onPageChange={skusPagination.goToPage}
                        totalItems={skusPagination.totalItems}
                        itemsPerPage={10}
                      />
                    </div>
                    <div className="flex flex-col gap-3 text-sm border-t p-4">
                      <div className="flex items-center gap-2 text-green-600">
                        <img src={checkTick} />
                        {skusData.filter((row) => !row.hasError).length} rows
                        valid
                      </div>
                      {skusData.filter((row) => row.hasError).length > 0 && (
                        <div className="flex items-center gap-2 text-red-600">
                          <img src={crossIcon} />
                          {skusData.filter((row) => row.hasError).length} rows
                          with issues -
                          {skusData.filter(
                            (row) => row.errorType === "duplicate"
                          ).length > 0 &&
                            `${skusData.filter((row) => row.errorType === "duplicate").length} duplicate SKU IDs, `}
                          {skusData.filter((row) => row.errorType === "missing")
                            .length > 0 &&
                            `${skusData.filter((row) => row.errorType === "missing").length} missing values`}
                        </div>
                      )}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </CardContent>
          </Card> */}
        </div>

        {/* Status Section */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Status
                <div className="flex items-center justify-between">
                  <div
                    className={`
          rounded-md px-4 py-2 font-medium text-sm
          ${
            overallTablesStatus === "Validated"
              ? "border border-green-700 text-green-700"
              : overallTablesStatus === "Partially Validated"
                ? "border border-yellow-600 text-yellow-600"
                : "border border-rose-700 text-rose-700"
          }
        `}
                  >
                    {overallTablesStatus}
                  </div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-6">
                <div className="space-y-2">
                  {/* Info Label */}
                  <div className="mb-6 rounded text-cyan-700 text-sm font-medium flex items-center justify-between">
                    <span className="flex gap-2">
                      <Info className="min-w-[1.2rem]" />{" "}
                      <span>
                        Please ensure that the data entered in the files (shown
                        on the left) is accurate and valid. This is necessary
                        for the tables below to be populated correctly. Any
                        incorrect or incomplete data may prevent the system from
                        processing the information properly. Once all required
                        data is valid, you will be able to proceed to the next
                        step.
                      </span>
                    </span>
                  </div>
                  <div className="space-y-2 mt-4">
                    {tableEntries.map(([key, isValid]) => (
                      <div
                        key={key}
                        className="flex items-center gap-2 text-sm"
                      >
                        <Sheet size={18} className="text-primary" />
                        <span className="text-zinc-800 text-base font-medium">
                          {formatLabel(key)}
                        </span>
                        {isValid ? (
                          <Check
                            size={16}
                            className="text-emerald-700 ml-auto"
                          />
                        ) : (
                          <AlertTriangle
                            size={16}
                            className="text-rose-700 ml-auto"
                          />
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <Button
                onClick={handleImport}
                disabled={!areAllTablesValidated}
                className="w-full"
                variant={areAllTablesValidated ? "default" : "default"}
              >
                {isLoadingImport ? (
                  <span className="flex items-center justify-center">
                    <span className="animate-spin mr-2 h-5 w-5 border-2 border-white border-t-transparent rounded-full" />
                    Importing...
                  </span>
                ) : (
                  "Import"
                )}
              </Button>
            </CardContent>
          </Card>

          {showUploadValidateModal && (
            <UploadValidateFileModal
              isOpen={showUploadValidateModal}
              onClose={() => setShowUploadValidateModal(false)}
              batchId={Number(selectedBatchId)}
              onValidateComplete={handleValidateComplete}
            />
          )}
        </div>
      </div>

      <style>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
}
