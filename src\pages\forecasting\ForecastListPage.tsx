import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Eye } from "lucide-react";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableHeader,
  TableRow,
  TableHead,
  TableBody,
  TableCell,
} from "@/components/ui/table";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { fetchForecastDropdown } from "@/hooks/forcastList/useForcastList";
import { useGetForcastList } from "@/hooks/forcastList/useForcastListHooks";
import { useGetStoreList } from "@/hooks/store/storeListHooks";
import { useToast } from "@/hooks/useToast";
import { ToastContainer } from "@/components/common/Toast";
import GlobalLoader from "@/components/common/GlobalLoader";

export default function ForecastListPage() {
  const navigate = useNavigate();
  const { toasts, removeToast, success, error } = useToast();
  const [selectedForecastName, setSelectedForecastName] = useState("");
  const [selectedStore, setSelectedStore] = useState("");
  const [forecastList, setForecastList] = useState<any[]>([]);
  const [storeList, setStoreList] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [filters, setFilters] = useState<{
    forecastName?: string;
    store?: string;
  }>({});
  const pageSize = 10;

  // Fetch forecast list with filters
  const {
    data: forcastListData,
    isLoading: isforcastLoading,
    error: forcastError,
  } = useGetForcastList(currentPage, pageSize, filters);

  // Fetch store list
  const { data: storeListData, isLoading: isStoreLoading } = useGetStoreList();

  // Forecast dropdown options
  const { data, isLoading, error: errorInfo } = fetchForecastDropdown();

  useEffect(() => {
    if (data) {
      setForecastList(data.result?.data || []);
    }
  }, [data]);

  useEffect(() => {
    if (storeListData) {
      setStoreList(storeListData?.data || []);
    }
  }, [storeListData]);

  const handleViewForecast = (forecastId: string, forecastData: any) => {
    navigate(`/forecasting/past/${forecastId}`, {
      state: { forecast: forecastData },
    });
  };

  const totalCount = forcastListData?.result?.total_items ?? 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  // if (isLoading || isforcastLoading || isStoreLoading) return <GlobalLoader />;
  if (errorInfo || forcastError) return <p>Error loading data.</p>;
  if (errorInfo) return <p>Error: {errorInfo.message}</p>;

  return (
    <>
      <ToastContainer toasts={toasts} onClose={removeToast} />
      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Filter</CardTitle>
        </CardHeader>
        <CardContent className="flex gap-4 pt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
            <div>
              <Label htmlFor="forecast-name" className="mb-2 block">
                Forecast Label
              </Label>
              <Select
                value={selectedForecastName}
                onValueChange={setSelectedForecastName}
              >
                <SelectTrigger id="forecast-name">
                  <SelectValue placeholder="Select forecast" />
                </SelectTrigger>
                <SelectContent>
                  {forecastList
                    .filter((item) => item?.name?.trim() !== "")
                    .map((item, index) => (
                      <SelectItem key={index} value={item.id}>
                        {item.name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="store" className="mb-2 block">
                Store
              </Label>
              <Select value={selectedStore} onValueChange={setSelectedStore}>
                <SelectTrigger id="store">
                  <SelectValue placeholder="Select Store" />
                </SelectTrigger>
                <SelectContent>
                  {storeList
                    .filter((item) => item?.location_name?.trim() !== "")
                    .map((item, index) => (
                      <SelectItem key={index} value={item.location_id}>
                        {item.location_name}
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-end gap-4">
            <Button
              onClick={() => {
                const newFilters: any = {};
                if (selectedForecastName)
                  newFilters.forecastName = selectedForecastName;
                if (selectedStore) newFilters.store = selectedStore;
                setFilters(newFilters);
                setCurrentPage(1);
              }}
            >
              Apply Filter
            </Button>

            <Button
              variant="outline"
              onClick={() => {
                setSelectedForecastName("");
                setSelectedStore("");
                setFilters({});
                setCurrentPage(1);
              }}
            >
              Clear
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Forecast List */}
      <Card>
        <CardHeader>
          <CardTitle>Forecast List</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading || isforcastLoading || isStoreLoading
            ?
            <GlobalLoader />
            :
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Forecast Name</TableHead>
                  <TableHead>Horizon</TableHead>
                  <TableHead>Created On</TableHead>
                  <TableHead className="text-center">Action</TableHead>
                </TableRow>
              </TableHeader>
              {
                forcastListData?.result?.data?.length > 0
                  ?
                  <TableBody>
                    {forcastListData?.result?.data?.map((forecast, index) => (
                      <TableRow key={index}>
                        <TableCell>{forecast.name}</TableCell>
                        <TableCell>{forecast.forecast_days} days</TableCell>
                        <TableCell>
                          {new Date(forecast.created_at).toLocaleDateString()}
                        </TableCell>
                        <TableCell className="text-center">
                          <Button
                            onClick={() => {

                              if (!forecast.forecast_progress_id) {
                                error(
                                  "Error",
                                  "There was an error generating the forecast."
                                );
                                return;
                              }
                              handleViewForecast(
                                forecast.forecast_progress_id,
                                forecast
                              );
                            }}
                            variant="ghost"
                            size="icon"
                            title="View forecast"
                          >
                            <Eye className="h-5 w-5" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                  :
                  <TableBody>
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                        No Data Found
                      </TableCell>
                    </TableRow>
                  </TableBody>
              }
            </Table>
          }

          {forcastListData?.result?.data?.length > 0 && !isLoading && (
            <div className="flex justify-between items-center py-3">
              <p className="text-sm text-gray-600">
                Showing{" "}
                {totalCount === 0 ? 0 : (currentPage - 1) * pageSize + 1}–
                {Math.min(currentPage * pageSize, totalCount)} of {totalCount}{" "}
                rows
              </p>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="px-3 py-1 rounded border text-sm"
                  disabled={currentPage === 1}
                  onClick={() => setCurrentPage((prev) => prev - 1)}
                >
                  &lt;
                </Button>
                {[...Array(totalPages)].map((_, i) => (
                  <Button
                    key={i}
                    className="rounded border"
                    variant={currentPage === i + 1 ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setCurrentPage(i + 1)}
                  >
                    {i + 1}
                  </Button>
                ))}
                <Button
                  variant="ghost"
                  size="sm"
                  className="rounded border"
                  disabled={currentPage === totalPages}
                  onClick={() => setCurrentPage((prev) => prev + 1)}
                >
                  &gt;
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  );
}
