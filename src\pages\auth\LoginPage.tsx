import { useState, useEffect } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { <PERSON>, EyeOff, Loader } from "lucide-react";
import { useAuthStore } from "@/store/authStore";
import FlashanaLogo from "@/components/common/FlashanaLogo";
import { useToast } from "@/hooks/useToast";
import { usePostSignin } from "@/hooks/auth/loginHooks";
import { ToastContainer } from "@/components/common/Toast";
import loginImg from "../../assets/login-building.png";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

const schema = yup.object({
  email: yup
    .string()
    .email("Invalid email")
    .required("Email is required")
    .matches(/^[^\s]+@[^\s]+\.[^\s]+$/, "Email must not contain spaces"),
  password: yup
    .string()
    .required("Password is required")
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z0-9])(?!.*\s).{8,}$/,
      "Invalid password (must include uppercase, lowercase, number, special character)"
    ),
});

export interface LoginFormData {
  email: string;
  password: string;
}
export default function LoginPage() {
  const storedAuth = localStorage.getItem("auth");
  const userInfo = JSON.parse(storedAuth);

  // Add state for default email and password
  const [defaultEmail, setDefaultEmail] = useState("");
  const [defaultPassword, setDefaultPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [rememberMe, setRememberMe] = useState(userInfo?.rememberMe || false);
  const { toasts, removeToast, success, error } = useToast();
  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);

  // On mount, check localStorage for userInfo
  useEffect(() => {
    const storedAuth = localStorage.getItem("auth");
    if (storedAuth) {
      try {
        const userInfo = JSON.parse(storedAuth);
        if (userInfo?.email) {
          setDefaultEmail(userInfo.email);
          setRememberMe(userInfo.rememberMe || false);
        }
        if (userInfo?.password) {
          setDefaultPassword(userInfo.password);
        }
      } catch (e) {
        // ignore parse errors
      }
    }
  }, []);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
  } = useForm<LoginFormData>({
    resolver: yupResolver(schema),
    defaultValues: {
      email: defaultEmail,
      password: defaultPassword,
    },
  });

  // Update email and password fields if defaults change
  useEffect(() => {
    if (defaultEmail) {
      setValue("email", defaultEmail);
    }
    if (defaultPassword) {
      setValue("password", defaultPassword);
    }
  }, [defaultEmail, defaultPassword, setValue]);

  const onPostSuccess = (data: any, rememberMe: boolean) => {
    setIsLoading(false);
    success("Success", `${data?.message || "Signin successful."}`);

    const responseData = data?.result?.data;
    const userInfo = {
      username: responseData?.user?.email || "",
      email: responseData?.user?.email || "",
      user_id: responseData?.user?.user_id || "",
      tenant_id: responseData?.user?.tenant_id || "",
      access_token: responseData?.token_data?.id_token || "",
      full_name: `${responseData?.user?.first_name || ""} ${responseData?.user?.last_name || ""}`.trim(),
      role: responseData?.roles?.[0] || "",
    };
    login(userInfo);


    setTimeout(() => {
      navigate("/dashboard");
    }, 3000);
  };

  const onPostError = (errorRes: any) => {
    setIsLoading(false);
    const errorInfo =
      errorRes?.error?.message || "Something went wrong. Please try again.";
    error("Error", `${errorInfo}`);
  };

  const { postMutate } = usePostSignin(onPostSuccess, onPostError);

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);



    try {
      const userDataPayload: any = {
        email: data?.email || "",
        password: data?.password || "",
      };

      if (rememberMe) {
        const userInfo = {
          email: data?.email || "",
          password: data?.password || "",
          rememberMe: rememberMe,
        };
        localStorage.setItem("auth", JSON.stringify(userInfo));
      } else {
        localStorage.removeItem("auth");
      }

      postMutate(userDataPayload, { rememberMe: rememberMe });
    } catch (error) {
      console.error("Login failed:", error);
      setIsLoading(false);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 h-screen">
      {/* Left side - Building image */}
      <div className="w-full h-full overflow-hidden flex items-center justify-center">
        <img src={loginImg} alt="Login" className="w-full h-full object-cover" />
      </div>

      {/* Right side - Login form */}
      <div className="wrapper flex justify-center bg-white p-[40px] h-screen min-h-0 overflow-y-auto">
        <div
          className="w-full max-w-md flex flex-col justify-center"
        >
          <div className="flex flex-col justify-center flex-1 py-6">
            {/* Logo and title */}
            <div className="text-center pb-[40px]">
              <div
                style={{
                  marginBottom: "40px",
                  display: "flex",
                  justifyContent: "center",
                }}
              >
                <FlashanaLogo variant="full" size="large" animated={false} />
              </div>
              <h2 className="text-3xl text-primary text-center font-semibold mb-4">
                Login
              </h2>
              <p className="text-zinc-500">
                Access your account by logging in below
              </p>
            </div>

            {/* Login form */}
            <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-4">
              {/* Email field */}
              <div>
                <Label htmlFor="email">
                  Email<span className="text-red-500">*</span>
                </Label>
                <Input
                  id="email"
                  type="text"
                  {...register("email")}
                  placeholder="Enter your email"
                  onInput={e => {
                    const target = e.target as HTMLInputElement;
                    target.value = target.value.replace(/\s/g, "");
                  }}

                  className={
                    errors.email
                      ? "border-destructive focus:ring-destructive focus:border-destructive focus-visible:ring-destructive focus-visible:border-destructive"
                      : ""
                  }
                />
                {errors.email && (
                  <p className="mt-1 text-xs text-destructive">
                    {errors.email.message}
                  </p>
                )}
              </div>

              {/* Password field */}
              <div>
                <Label htmlFor="password">
                  Password<span className="text-red-500">*</span>
                </Label>
                <div className="relative">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    autoComplete="current-password"
                    {...register("password")}
                    placeholder="Enter your password"
                    onInput={e => {
                      const target = e.target as HTMLInputElement;
                      target.value = target.value.replace(/\s/g, "");
                    }}

                    className={
                      errors.password
                        ? "border-destructive pr-10 focus-visible:ring-destructive focus-visible:border-destructive"
                        : "pr-10"
                    }
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-zinc-500"
                    tabIndex={-1}
                  >
                    {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-xs text-destructive">
                    {errors.password.message}
                  </p>
                )}
              </div>

              {/* Remember me and forgot password */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="remember-me"
                    checked={rememberMe}
                    onCheckedChange={(checked) => setRememberMe(!!checked)}
                  />
                  <Label htmlFor="remember-me" className="text-sm">
                    Remember me
                  </Label>
                </div>
                <Link
                  to="/auth/forgot-password"
                  className="text-sm text-secondary font-medium hover:underline"
                >
                  Forgot password?
                </Link>
              </div>

              {/* Login button */}
              <button
                type="submit"
                disabled={isLoading}
                style={{
                  width: "100%",
                  backgroundColor: isLoading ? "#94a3b8" : "#2b524f",
                  color: "white",
                  borderRadius: "0.375rem",
                  padding: "0.625rem 1.5rem",
                  fontWeight: "500",
                  fontSize: "1rem",
                  border: "none",
                  cursor: isLoading ? "not-allowed" : "pointer",
                  opacity: isLoading ? 0.5 : 1,
                  transition: "all 0.2s",
                }}
                onMouseOver={(e: any) =>
                  !isLoading && (e.target.style.backgroundColor = "#1f413e")
                }
                onMouseOut={(e: any) =>
                  !isLoading && (e.target.style.backgroundColor = "#2b524f")
                }
              >
                {isLoading ? (
                  <div
                    style={{
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    <div
                      style={{
                        width: "1.25rem",
                        height: "1.25rem",
                        border: "2px solid rgba(255, 255, 255, 0.3)",
                        borderTop: "2px solid white",
                        borderRadius: "50%",
                        animation: "spin 1s linear infinite",
                        marginRight: "0.5rem",
                      }}
                    />
                    Logging in...
                  </div>
                ) : (
                  "Log In"
                )}
              </button>

              {/* Sign up link */}
              <div className="text-center py-2">
                <p className="text-sm text-zinc-500">
                  Don't have an account?{" "}
                  <Link
                    to="/auth/signup"
                    className="text-secondary font-medium hover:underline"
                  >
                    Sign Up
                  </Link>
                </p>
              </div>
            </form>
          </div>
          {/* Terms and Privacy */}
          <div className="terms text-center">
            <p className="text-xs text-zinc-500">
              By logging in you agree to our{" "}

              {/* When /terms and /privac routes and pages are ready uncomment below Links and remove the text tags */}

              {/* <Link to="/terms" className="text-secondary font-medium hover:underline">
                Terms of Service
              </Link>{" "}
              and{" "}
              <Link to="/privacy" className="text-secondary font-medium hover:underline">
                Privacy Policy
              </Link> */}

              <text className="text-secondary font-medium hover:underline">
                Terms of Service
              </text>{" "}
              and{" "}
              <text className="text-secondary font-medium hover:underline">
                Privacy Policy
              </text>
              .
            </p>
          </div>
        </div>
      </div>
      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={removeToast} />
    </div>
  );
}
