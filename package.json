{"name": "flashana-frontend", "version": "1.0.0", "description": "React frontend for Flashana - Supply Chain Data Standardization & Demand Forecasting", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{ts,tsx,css,json}\""}, "dependencies": {"@dnd-kit/core": "^6.0.8", "@dnd-kit/sortable": "^7.0.2", "@dnd-kit/utilities": "^3.2.1", "@hookform/resolvers": "^5.1.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.0.0", "@types/react-query": "^1.2.8", "add": "^2.0.6", "alert": "^6.0.2", "axios": "^1.6.2", "chart.js": "^4.4.1", "chartjs-plugin-zoom": "^2.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-state-city": "^3.2.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "input-otp": "^1.4.2", "lucide-react": "^0.519.0", "notistack": "^3.0.2", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-date-range": "^2.0.1", "react-day-picker": "^9.7.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-google-recaptcha": "^3.1.0", "react-google-recaptcha-v3": "^1.11.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-phone-input-2": "^2.15.1", "react-query": "^3.39.3", "react-router-dom": "^6.20.0", "recharts": "^3.0.2", "shadcn": "^2.9.2", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "xlsx": "^0.18.5", "yup": "^1.3.3", "zustand": "^4.4.7"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query-devtools": "^5.80.6", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.21", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.5.6", "prettier": "^3.1.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "vite": "^5.0.0"}}